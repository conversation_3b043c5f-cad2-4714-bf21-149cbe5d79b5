import {
  Given,
  Then,
  Before,
  DataTable,
  When,
} from '@badeball/cypress-cucumber-preprocessor';
import { landingPage } from '../../../pages/landingPage';
import { mediaListPage } from '../../../pages/mediaListPage';

Before(() => {
  landingPage.loginLandingPage();
  cy.NavigateToTestFolder();
  // Replace fixed wait with a conditional wait for UI readiness.
  // This ensures the page (specifically the files table rows) is loaded
  // before proceeding.
  cy.contains('FILES').click();
  cy.get('[data-testid^=files-table-row]')
    .should('be.visible')
    .and('have.length.gt', 0);
  cy.getByRoles('progressbar').should('not.exist');
  cy.intercept('POST', /v3\/graphql/, (req) => {
    if (req.body.query && req.body.query.includes('temporalDataObject')) {
      req.alias = 'fetchFileList';
    }
  });
});

Given('The user is on the Files tab', () => {
  mediaListPage.getFilesTab().should('be.visible');
  mediaListPage.getFilesTab().should('have.attr', 'aria-selected', 'true');
  cy.getByRoles('progressbar').should('not.exist');
  cy.assertNoLoading();
});

Then(
  'The following columns should be visible in the files table:',
  (dataTable: DataTable) => {
    const expectedHeaders = dataTable.raw().map((row: string[]) => row[0]);
    mediaListPage.getTableHeaders().each(($header, index) => {
      const expectedHeader = expectedHeaders[index];
      if (expectedHeader) {
        cy.wrap($header)
          .find('span[role="button"]')
          .find('span')
          .first()
          .invoke('text')
          .should('eq', expectedHeader);
      } else {
        cy.wrap($header)
          .find('span[role="button"]')
          .find('span')
          .first()
          .invoke('text')
          .invoke('trim')
          .should('be.empty');
      }
    });
  }
);

Then('Each file row should have the correct data', () => {
  mediaListPage.verifyFileTableRows();
});

Then('The pagination control should be visible', () => {
  mediaListPage.getPagination().should('be.visible');
});

Then(
  'The total files counter should display {string}',
  (expectedCount: string) => {
    cy.get('[data-testid="file-paging"]').contains(`of ${expectedCount}`);
  }
);

When(
  'The user sorts the {string} column by {string}',
  (columnName: string, sortedBy: 'a-z' | 'z-a') => {
    mediaListPage.sortColumn(columnName, sortedBy);
  }
);

Then(
  'The table is sorted by the {string} column in {string} order',
  (columnName: string, sortOrder: 'a-z' | 'z-a') => {
    cy.verifyTableSort(columnName, sortOrder);
  }
);

When('The user clicks on checkbox for each file', () => {
  mediaListPage.selectAllFiles();
});

When('The user clicks the select all checkbox', () => {
  mediaListPage.clickSelectAllCheckbox();
});

Then('All selected files are checked in checkbox', () => {
  mediaListPage.verifyAllFilesSelected();
});
