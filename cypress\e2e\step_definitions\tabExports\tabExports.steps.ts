import {
  When,
  Before,
  Given,
  Then,
  DataTable,
} from '@badeball/cypress-cucumber-preprocessor';
import { landingPage } from '../../../pages/landingPage';
import { mediaListPage } from '../../../pages/mediaListPage';
import { exportPage } from '../../../pages/exportPage';

Before(() => {
  landingPage.loginLandingPage();
  mediaListPage.goToMediaListPage();
});

Given('The user navigates to the tab {string}', (tabName: string) => {
  exportPage.navigateToHistoryTab(tabName);
});

Then(
  'The {string} tab should display the total number of files',
  (tabName: string) => {
    exportPage.verifyTabDisplaysTotalFiles(tabName);
  }
);

Then(
  'The exports table should have the following columns:',
  (dataTable: DataTable) => {
    const columns = dataTable
      .raw()
      .map((row) => row[0])
      .filter(Boolean) as string[];
    exportPage.verifyExportsTableColumns(columns);
  }
);

Then('The table should have pagination controls', () => {
  exportPage.verifyTableHasPaginationControls();
});

Then(
  'Each export file name in the list should follow the default format',
  () => {
    exportPage.verifyExportFileNamesFollowDefaultFormat();
  }
);

Then(
  'Each export file in the list should have a {string} status',
  (expectedStatus: string) => {
    exportPage.verifyExportFilesHaveStatus(expectedStatus);
  }
);

Then(
  'The date initiated for each export file should follow the correct format',
  () => {
    exportPage.verifyDateInitiatedFormat();
  }
);

When('The user clicks the download button for an export file', () => {
  exportPage.clickDownloadButtonForExportFile();
});

Given(
  'The rows per page selector defaults to {string}',
  (defaultRows: string) => {
    exportPage.verifyRowsPerPageDefault(defaultRows);
  }
);

Then(
  'The user verifies changing the rows per page for the following values:',
  (dataTable: DataTable) => {
    const rowsPerPageValues = dataTable
      .raw()
      .map((row) => row[0])
      .filter(Boolean) as string[];
    exportPage.verifyChangingRowsPerPage(rowsPerPageValues);
  }
);

Then(
  'The pagination controls for the {string} page are correct',
  (page: string) => {
    exportPage.verifyPaginationControlsForPage(page);
  }
);

When('The user clicks the {string} page button', (direction: string) => {
  exportPage.clickPageButton(direction);
});
