#!/bin/bash
set -e

REPORT_FILE="cypress/reports/cucumber.json"
SLACK_MESSAGE=""
MAX_INLINE_FAILURES=3

if [ -f "$REPORT_FILE" ]; then
  # Step 1: Generate summary from JQ script
  FEATURE_SUMMARY=$(jq -r -f .github/scripts/e2e-report-handler.jq "$REPORT_FILE")

  # Step 2: Extract header and body
  HEADER=$(echo "$FEATURE_SUMMARY" | head -n 1)
  BODY=$(echo "$FEATURE_SUMMARY" | tail -n +2)

  # Step 3: Initialize max column widths
  IFS=$'\t' read -r -a header_cols <<< "$HEADER"
  MAX_FEATURE_LEN=${#header_cols[0]}
  MAX_TOTAL_LEN=${#header_cols[1]}
  MAX_PASSED_LEN=${#header_cols[2]}
  MAX_FAILED_LEN=${#header_cols[3]}
  MAX_SKIPPED_LEN=${#header_cols[4]}

  while IFS=$'\t' read -r -a cols; do
    (( ${#cols[0]} > MAX_FEATURE_LEN )) && MAX_FEATURE_LEN=${#cols[0]}
    (( ${#cols[1]} > MAX_TOTAL_LEN )) && MAX_TOTAL_LEN=${#cols[1]}
    (( ${#cols[2]} > MAX_PASSED_LEN )) && MAX_PASSED_LEN=${#cols[2]}
    (( ${#cols[3]} > MAX_FAILED_LEN )) && MAX_FAILED_LEN=${#cols[3]}
    (( ${#cols[4]} > MAX_SKIPPED_LEN )) && MAX_SKIPPED_LEN=${#cols[4]}
  done < <(echo "$BODY")

  # Step 4: Format header row
  FORMATTED_HEADER=$(printf "%-${MAX_FEATURE_LEN}s %-${MAX_TOTAL_LEN}s %-${MAX_PASSED_LEN}s %-${MAX_FAILED_LEN}s %-${MAX_SKIPPED_LEN}s" \
    "${header_cols[0]}" "${header_cols[1]}" "${header_cols[2]}" "${header_cols[3]}" "${header_cols[4]}")

  # Step 5: Format body rows
  FORMATTED_BODY_LINES=()
  while IFS=$'\t' read -r -a cols; do
    FORMATTED_LINE=$(printf "| %-${MAX_FEATURE_LEN}s %-${MAX_TOTAL_LEN}s %-${MAX_PASSED_LEN}s %-${MAX_FAILED_LEN}s %-${MAX_SKIPPED_LEN}s |" \
      "${cols[0]}" "${cols[1]}" "${cols[2]}" "${cols[3]}" "${cols[4]}")
    FORMATTED_BODY_LINES+=("$FORMATTED_LINE")
  done < <(echo "$BODY")

  # Step 6: Calculate totals (safely via jq)
  TOTALS=($(jq -r '
    .[] |
    select(type == "object") |
    {
      total: (.elements | length),
      passed: (
        [.elements[]? |
          select(
            (.steps | all(.result.status == "passed"))
          )
        ] | length
      ),
      failed: (
        [.elements[]? |
          select(
            (.steps[]?.result.status == "failed")
          )
        ] | length
      ),
      skipped: (
        [.elements[]? |
          select(
            (.steps | all(.result.status == "skipped"))
          )
        ] | length
      )
    } | [.total, .passed, .failed, .skipped] | @tsv
  ' "$REPORT_FILE" | awk -F'\t' '{t+=$1;p+=$2;f+=$3;s+=$4} END {print t,p,f,s}'))

  # Step 7: Format totals
  TOTAL_DISPLAY=()
  for val in "${TOTALS[@]}"; do
    if [[ "$val" -eq 0 ]]; then
      TOTAL_DISPLAY+=("-")
    else
      TOTAL_DISPLAY+=("$val")
    fi
  done

  TOTAL_ROW=$(printf "| %-${MAX_FEATURE_LEN}s %-${MAX_TOTAL_LEN}s %-${MAX_PASSED_LEN}s %-${MAX_FAILED_LEN}s %-${MAX_SKIPPED_LEN}s |" \
    "Total" "${TOTAL_DISPLAY[0]}" "${TOTAL_DISPLAY[1]}" "${TOTAL_DISPLAY[2]}" "${TOTAL_DISPLAY[3]}")
  FORMATTED_BODY_LINES+=("$TOTAL_ROW")

  FORMATTED_BODY=$(printf "%s\n" "${FORMATTED_BODY_LINES[@]}")
  LINE_LENGTH=$(( MAX_FEATURE_LEN + MAX_TOTAL_LEN + MAX_PASSED_LEN + MAX_FAILED_LEN + MAX_SKIPPED_LEN + 6 ))
  SEPARATOR=$(printf "%-${LINE_LENGTH}s" | tr ' ' '-')

  FULL_TABLE_OUTPUT="\`\`\`\n"
  FULL_TABLE_OUTPUT+="+$SEPARATOR+\n"
  FULL_TABLE_OUTPUT+="| $FORMATTED_HEADER |\n"
  FULL_TABLE_OUTPUT+="+$SEPARATOR+\n"
  FULL_TABLE_OUTPUT+="$FORMATTED_BODY\n"
  FULL_TABLE_OUTPUT+="+$SEPARATOR+\n"
  FULL_TABLE_OUTPUT+="\`\`\`"

  # Step 8: Count failures (based on jq summary, not failed_scenarios list)
  FAILED_SCENARIOS_COUNT=${TOTAL_DISPLAY[2]}
  [[ "$FAILED_SCENARIOS_COUNT" == "-" ]] && FAILED_SCENARIOS_COUNT=0

  # Step 9: Optional failed scenario details
  FAILED_SCENARIOS=$(jq -r '
    .[] as $feature
    | $feature.elements[]?
    | select((.steps | any(.result.status == "failed")) or (.steps == null))
    | "- *Feature:* `\(($feature.uri // "" | gsub("\\\\"; "/") | split("/") | last | sub(".feature$"; "")))`\n  *Scenario:* `\(.name)`"
  ' "$REPORT_FILE")

  # Step 10: Set test status
  TEST_STATUS="SUCCESS"
  if [ "$FAILED_SCENARIOS_COUNT" -gt 0 ]; then
    TEST_STATUS="FAILURE"
  fi

  # Step 11: Construct Slack message
  SLACK_MESSAGE="*Illuminate Cucumber Test Report - Build ${GITHUB_RUN_NUMBER}*"
  SLACK_MESSAGE+="\n*Status:* $TEST_STATUS"
  SLACK_MESSAGE+="\n*Branch:* \`${GITHUB_REF_NAME}\`"
  SLACK_MESSAGE+="\n*Latest Commit:* \`${GITHUB_SHA::7}\`"
  SLACK_MESSAGE+="\n*Workflow Link:* <${GITHUB_SERVER_URL}/${GITHUB_REPOSITORY}/actions/runs/${GITHUB_RUN_ID}|View Workflow>"
  SLACK_MESSAGE+="\n\n*Summary Per Feature:*\n$FULL_TABLE_OUTPUT"

  if [ "$FAILED_SCENARIOS_COUNT" -gt 0 ]; then
    if [ "$FAILED_SCENARIOS_COUNT" -le "$MAX_INLINE_FAILURES" ]; then
      SLACK_MESSAGE+="\n\n*Failed Scenarios Details:*\n$FAILED_SCENARIOS"
    else
      SLACK_MESSAGE+="\n\n*Too many failed scenarios to list.* [View full details in the workflow logs.]"
    fi
  else
    SLACK_MESSAGE+="\n\nAll scenarios passed or were skipped!"
  fi

  # Step 12: Output
  {
    echo "SLACK_MESSAGE<<EOF"
    echo -e "$SLACK_MESSAGE"
    echo "EOF"
    echo "SLACK_MESSAGE_STATUS=$TEST_STATUS"
  } >> "$GITHUB_OUTPUT"

else
  echo "SLACK_MESSAGE<<EOF" >> "$GITHUB_OUTPUT"
  echo "*Cypress Cucumber Report Not Found!* Unable to generate a detailed report." >> "$GITHUB_OUTPUT"
  echo "EOF" >> "$GITHUB_OUTPUT"
  echo "SLACK_MESSAGE_STATUS=FAILURE" >> "$GITHUB_OUTPUT"
fi
