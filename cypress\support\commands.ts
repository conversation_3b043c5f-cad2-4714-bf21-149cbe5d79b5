// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
import { fileTabName } from '../fixtures/variables';
import 'cypress-file-upload';

Cypress.Commands.add('LoginToApp', () =>
  cy
    .request({
      method: 'POST',
      url: `${Cypress.env('apiRoot')}/v1/admin/login`,
      form: true,
      body: {
        userName: Cypress.env('username'),
        password: Cypress.env('password'),
      },
    })
    .then((userInfo: Cypress.Response<LoginResponse>) => {
      Cypress.env('orgId', userInfo.body.organization.organizationId);
      Cypress.env('token', userInfo.body.token);
      Cypress.env('userId', userInfo.body.userId);
      return userInfo;
    })
);

Cypress.Commands.add('LoginLandingPage', () => {
  cy.LoginToApp();
  cy.visit('/');
  // wait for initial load completed.
  // Ensure the dashboard element is visible to confirm successful login and page load.
  cy.get('[data-test="dashboard-card/render-card/Speaker Detection"]', {
    timeout: 60000,
  }).should('be.visible');
});

// Remove the old PreserveSession command as cy.session() will handle this.
// Cypress.Commands.add('PreserveSession', () => {
//   // azure prod, azure stage, aws prod : veritone-session-id
//   // aws dev :   dev-veritone-session-id
//   // aws stage : stage-veritone-session-id,
//   // uk prod :   uk-prod-veritone-session-id
//   Cypress.Cookies.preserveOnce(
//     'veritone-session-id',
//     'dev-veritone-session-id',
//     'stage-veritone-session-id',
//     'uk-prod-veritone-session-id'
//   );
// });

Cypress.Commands.add(
  'LoginAndSetupSession',
  (sessionId = 'defaultUserSession') => {
    cy.session(
      sessionId,
      () => {
        // This setup function runs once per session ID, or when validation fails.
        // It should contain the steps to log in and establish the session.
        cy.LoginLandingPage(); // Re-use your existing command that logs in and lands on the page
      },
      {
        validate() {
          // This function runs before restoring a cached session.
          // Check if one of the known session cookies exists.
          // cy.session() automatically preserves cookies set during its setup.
          const knownSessionCookieNames = [
            'veritone-session-id',
            'dev-veritone-session-id',
            'stage-veritone-session-id',
            'uk-prod-veritone-session-id',
          ] as const;
          cy.getCookie(knownSessionCookieNames[0]).should('exist');
        },
      }
    );
  }
);
// // New way using cy.session()
// beforeEach(() => {
//   cy.LoginAndSetupSession();
// });

// folder e2e/mediaDetail is used as test folder. it is seeded with 3 files:
// e2e_audio.mp3
// e2e_video.mp4
// bloomberg.mp4
Cypress.Commands.add('NavigateToTestFolder', () => {
  cy.get('[data-test="top-bar-select-folder"]').click({ force: true });
  cy.get('[data-test="folder-modal-dialog-content"]').contains('e2e').click();
  cy.get('[data-test="folder-modal-dialog-content"]')
    .contains('mediaDetail')
    .click();
  cy.get('[data-test=folder-modal-select-folder-button]').click();
  cy.assertNoLoading();
});

Cypress.Commands.add('GoToTestFolder', () => {
  cy.NavigateToTestFolder();
  cy.contains(fileTabName).click();
});

// folder e2e/upload is used for upload test. e2e/mediaDetail folder has seeded files, so
// the files created in upload test need to be a different folder to avoid interfere the
// other tests
Cypress.Commands.add('NavigateToUploadFolder', () => {
  cy.log('🚀 Starting navigation to upload folder');

  // Step 1: Click the folder selection button
  cy.log('📂 Step 1: Opening folder selection dialog');
  cy.get('[data-test="top-bar-select-folder"]', { timeout: 10000 })
    .should('be.visible')
    .then(($el) => {
      cy.log(`Found folder button: ${$el.text()}`);
    })
    .click({ force: true });

  // Step 2: Wait for dialog to appear and click e2e folder
  cy.log('📂 Step 2: Waiting for folder dialog and selecting e2e folder');
  cy.get('[data-test="folder-modal-dialog-content"]', { timeout: 10000 })
    .should('be.visible')
    .then(($dialog) => {
      cy.log(`Dialog content visible, looking for folders...`);
      cy.wrap($dialog).find('*').contains('e2e').should('exist');
    })
    .within(() => {
      cy.contains('e2e', { timeout: 5000 })
        .should('be.visible')
        .then(($e2e) => {
          cy.log(`Found e2e folder: ${$e2e.text()}`);
        })
        .click();
    });

  // Step 3: Click upload subfolder
  cy.log('📂 Step 3: Selecting upload subfolder');
  cy.get('[data-test="folder-modal-dialog-content"]')
    .then(($dialog) => {
      cy.log(`Looking for upload subfolder...`);
      cy.wrap($dialog).find('*').contains('upload').should('exist');
    })
    .contains('upload', { timeout: 5000 })
    .should('be.visible')
    .then(($upload) => {
      cy.log(`Found upload folder: ${$upload.text()}`);
    })
    .click();

  // Step 4: Confirm selection
  cy.log('📂 Step 4: Confirming folder selection');
  cy.get('[data-test=folder-modal-select-folder-button]', { timeout: 5000 })
    .should('be.visible')
    .should('not.be.disabled')
    .then(($btn) => {
      cy.log(`Select button state: enabled=${!$btn.prop('disabled')}`);
    })
    .click();

  // Step 5: Verify navigation completed
  cy.log('📂 Step 5: Verifying navigation completed');
  cy.get('[data-test="folder-modal-dialog-content"]', { timeout: 5000 })
    .should('not.exist');

  // Additional verification - check if we can see breadcrumbs or current folder indicator
  cy.log('📂 Step 6: Additional verification');
  cy.get('body').then(($body) => {
    if ($body.find('[data-test="top-bar-breadcrumbs"]').length > 0) {
      cy.get('[data-test="top-bar-breadcrumbs"]').then(($breadcrumbs) => {
        cy.log(`Current breadcrumbs: ${$breadcrumbs.text()}`);
      });
    }
  });

  cy.log('✅ Successfully navigated to upload folder');
});

// Add a more robust version with error handling
Cypress.Commands.add('NavigateToUploadFolderRobust', () => {
  cy.log('🚀 Starting robust navigation to upload folder');

  // First, let's see what folders are available
  cy.get('[data-test="top-bar-select-folder"]', { timeout: 10000 })
    .should('be.visible')
    .click({ force: true });

  cy.get('[data-test="folder-modal-dialog-content"]', { timeout: 10000 })
    .should('be.visible')
    .then(($dialog) => {
      // Log all available folders for debugging
      const folderTexts = [];
      $dialog.find('*').each((index, element) => {
        const text = Cypress.$(element).text().trim();
        if (text && text.length > 0 && text.length < 50) {
          folderTexts.push(text);
        }
      });
      cy.log(`Available folders/items: ${[...new Set(folderTexts)].join(', ')}`);

      // Try to find e2e folder
      if ($dialog.find('*:contains("e2e")').length > 0) {
        cy.log('✅ Found e2e folder');
        cy.wrap($dialog).contains('e2e').click();

        // Look for upload subfolder
        cy.get('[data-test="folder-modal-dialog-content"]').then(($updatedDialog) => {
          if ($updatedDialog.find('*:contains("upload")').length > 0) {
            cy.log('✅ Found upload subfolder');
            cy.wrap($updatedDialog).contains('upload').click();
          } else {
            cy.log('❌ Upload subfolder not found, staying in e2e folder');
          }
        });
      } else {
        cy.log('❌ e2e folder not found, staying in current folder');
      }

      // Try to select whatever folder we're in
      cy.get('[data-test=folder-modal-select-folder-button]', { timeout: 5000 })
        .should('be.visible')
        .click();
    });

  cy.log('✅ Robust navigation completed');
});

// folder e2e/reprocess is used for reprocess test.
Cypress.Commands.add('NavigateToReprocessFolder', (folderName: string) => {
  cy.get('[data-test="top-bar-select-folder"]').click({ force: true });
  cy.get('[data-test="folder-modal-dialog-content"]').contains('e2e').click();
  cy.get('[data-test="folder-modal-dialog-content"]')
    .contains(folderName)
    .click();
  cy.get('[data-test=folder-modal-select-folder-button]').click();
});

// remove tag
Cypress.Commands.add('RemoveTag', (rowId: number, tagName: string) => {
  // check the tdo row
  cy.get('[data-test="files-table-row"]')
    .eq(rowId)
    .find('input[type="checkbox"]')
    .check();
  // clear the tag
  cy.get('[data-test="files-bulk-tag-icon-button"]').click();
  cy.get('[data-test="tag-autocomplete"]').contains(tagName).next().click();
  cy.get('[data-test="tag-dialog-save-button"]').click();

  // verify tag removed
  cy.contains('Successfully tagged files').next().click();
});

Cypress.Commands.add(
  'repeat',
  ({ action, times }: { action: unknown; times: number }) => {
    if (typeof action === 'function') {
      Array.from({ length: times }, () => action());
    }
  }
);

Cypress.Commands.add(
  'awaitNetworkResponseCode',
  ({
    alias,
    code,
    repeat = 1,
  }: {
    alias: string;
    code: number;
    repeat?: number;
  }) => {
    cy.repeat({
      action: cy.wait(`${alias}`).its('response.statusCode').should('eq', code),
      times: repeat,
    });
    // cy.assertNoLoading();
  }
);

Cypress.Commands.add(
  'getDataIdCy',
  ({
    idAlias,
    options = {},
  }: {
    idAlias: string;
    options?: Partial<Cypress.Loggable & Cypress.Timeoutable & Cypress.Shadow>;
  }) => {
    const matches = idAlias.replace(/@/, '').split(' > ');

    const tagName = matches[0];
    const childCombinators: string | string[] =
      matches.slice(1).join(' > ') ?? '';
    const withChildCombinators =
      childCombinators.length > 0 ? ` > ${childCombinators}` : '';

    const selectorTestId = `[data-testid="${tagName}"]${withChildCombinators}`;
    const selectorTest = `[data-test="${tagName}"]${withChildCombinators}`;

    return cy.get(`${selectorTestId}, ${selectorTest}`, options);
  }
);

// Cypress.Commands.add('CreateNewFolder', anewFolder => {
//   cy.get('[data-test="top-bar-select-folder"]').should('be.visible');
//   cy.get('[data-test="top-bar-select-folder"]').click();
//   cy.get('[data-test="root-folder"]')
//     .contains('MY CASES')
//     .click({ timeout: 10000 });
//   cy.get('[data-test="folder-modal-create-new-folder-button"]').click();
//   cy.contains('Create Folder');
//   cy.contains('Create folder in My Cases');
//   cy.get('[data-test="create-folder-enter-folder-name"] input').type(
//     anewFolder
//   );
//   cy.get('[data-test="create-folder-submit-button"]').click();
// });

// Cypress.Commands.add('filterFile', fileType => {
//   cy.contains('My Cases');
//   cy.get('[data-test="files-tab-button"]', { timeout: 10000 }).click({
//     force: true
//   });

//   cy.get('[data-test="files-table-row"]').should('be.visible');
//   cy.contains('FILES');
//   cy.get('[data-test="top-bar-filters-button"]').click();
//   cy.contains('FILTERS');
//   cy.get('[data-test="FILE TYPES"]').click();
//   cy.get(`[id="${fileType}"]`).check({ force: true });
//   cy.get('[data-test="filter-apply-filter"]').click({ force: true });
//   cy.get('[data-test="files-table-row"]').should('be.visible');
// });

// Cypress.Commands.add('filterFileType', () => {
//   cy.contains('My Cases');
//   cy.get('[data-test="files-tab-button"]', { timeout: 10000 }).click({
//     force: true
//   });

//   cy.get('[data-test="files-table-row"]').should('be.visible');
//   cy.contains('FILES');
//   cy.get('[data-test="top-bar-filters-button"]').click();
//   cy.contains('FILTERS');
//   cy.get('[data-test="FILE TYPES"]').click();
//   cy.get('[id="video"]').check({ force: true });
//   cy.get('[id="audio"]').check({ force: true });
//   cy.get('[data-test="filter-apply-filter"]').click({ force: true });
//   cy.get('[data-test="files-table-row"]').should('be.visible');
// });

// Cypress.Commands.add('filterDateRange', () => {
//   cy.contains('My Cases');
//   cy.get('[data-test="files-tab-button"]', { timeout: 10000 }).click({
//     force: true
//   });

//   cy.get('[data-test="files-table-row"]').should('be.visible');
//   cy.contains('FILES');
//   cy.get('[data-test="top-bar-filters-button"]').click();
//   cy.contains('FILTERS');
//   cy.get('[data-test="DATE RANGE"]').click();
//   cy.get('[data-test="filter-section-date-range"]')
//     .find('[id="startDate"]')
//     .type(startOfYear);
//   cy.get('[data-test="filter-section-date-range"]')
//     .find('[id="endDate"]')
//     .type(todaysDate);
//   cy.get('[data-test="filter-apply-filter"]').click({ force: true });
//   cy.get('[data-test="files-table-row"]').should('be.visible');
// });

// Cypress.Commands.add('filter Enity Types', () => {
//   cy.contains('My Cases');
//   cy.get('[data-test="files-tab-button"]', { timeout: 10000 }).click({
//     force: true
//   });
//   cy.contains('FILES');
//   Cy.get('[data-test="top-bar-filters-button"]').click();
//   cy.contains('FILTERS');
//   cy.get('[data-test="filter-section-entity-types"]', { timeout: 5000 })
//     .should('be.visible')
//     .then(() => {
//       cy.get(
//         'div:nth-child(1) > label:nth-child(2) input[type="radio"]'
//       ).click({ force: true });
//     });
//   cy.get('[data-test="filter-apply-filter"]').click({ force: true });
// });

Cypress.Commands.add(
  'verifyTableSort',
  (columnName: string, sortOrder: 'a-z' | 'z-a') => {
    cy.get('[data-testid^=files-table-row]').should('have.length.gt', 0);

    return cy
      .contains('th', columnName)
      .invoke('index')
      .then((columnIndex) => {
        const cellValues: string[] = [];

        return cy
          .get('[data-testid^=files-table-row]')
          .should('have.length.gt', 0)
          .then(($rows) => {
            $rows.each((_index, row) => {
              const cellText = Cypress.$(row)
                .find('td')
                .eq(columnIndex)
                .text()
                .trim();
              cellValues.push(cellText);
            });

            const sortedValues = [...cellValues].sort((a, b) =>
              a.localeCompare(b, undefined, { numeric: true })
            );

            if (sortOrder === 'z-a') {
              sortedValues.reverse();
            }

            expect(cellValues).to.deep.equal(sortedValues);
            return null;
          });
      });
  }
);

Cypress.Commands.add('getByRoles', function (role: string) {
  const filters =
    role.split(':').length > 1 ? ':' + role.split(':').slice(1).join(':') : '';

  if (filters === '') {
    this.role = role;
  } else {
    this.role = role.split(':')[0];
  }

  return cy.get(`[role="${this.role}"]${filters}`);
});

Cypress.Commands.add(
  'interceptGraphQLQuery',
  (query: string, alias: string) => {
    cy.intercept('POST', /v3\/graphql/, (req) => {
      if (req.body.query === query) {
        req.alias = alias;
      }
    });
  }
);

Cypress.Commands.add('getState', () => {
  return cy.window().its('store').invoke('getState');
});

Cypress.Commands.add('assertNoLoading', () => {
  Cypress.config('defaultCommandTimeout', 30000);

  cy.getByRoles('progressbar').should('not.exist');
  cy.getState().should((state) => {
    expect(state.folders.fetchingFolders).to.not.equal(true);
    expect(state.tdosTable.isShowPreview).to.not.equal(true);
    expect(state.tdosTable.initFullScreen).to.not.equal(true);
    expect(state.search.searching).to.not.equal(true);
    expect(state.search.fetchingTdos).to.not.equal(true);
    expect(state.user.isFetching).to.not.equal(true);
  });
});

Cypress.Commands.add('deleteExportTemplate', (templateName: string) => {
  cy.window().then((win) => {
    const apiRoot = Cypress.env('apiRoot');
    const token = Cypress.env('token');

    const exportTemplateDataRegistryId =
      win.config?.exportTemplateDataRegistryId;

    if (!exportTemplateDataRegistryId) {
      return cy.log(
        'Export template data registry ID not found in config - skipping delete'
      );
    }

    const getSchemaQuery = `
      query getDataRegistry {
        dataRegistry(id: "${exportTemplateDataRegistryId}") {
          id
          name
          publishedSchema {
            id
          }
        }
      }
    `;

    cy.request({
      method: 'POST',
      url: `${apiRoot}/v3/graphql`,
      headers: { Authorization: `Bearer ${token}` },
      body: {
        query: getSchemaQuery,
        variables: {},
      },
      failOnStatusCode: false,
    }).then((schemaResponse) => {
      if (
        schemaResponse.status !== 200 ||
        !schemaResponse.body?.data?.dataRegistry?.publishedSchema?.id
      ) {
        return cy.log(
          'Could not retrieve schema ID - skipping template delete'
        );
      }

      const schemaId = schemaResponse.body.data.dataRegistry.publishedSchema.id;

      const getTemplatesQuery = `
        query getSdos {
          structuredDataObjects(schemaId: "${schemaId}") {
            count
            records {
              id
              data
              createdDateTime
            }
          }
        }
      `;

      cy.request({
        method: 'POST',
        url: `${apiRoot}/v3/graphql`,
        headers: { Authorization: `Bearer ${token}` },
        body: {
          query: getTemplatesQuery,
          variables: {},
        },
        failOnStatusCode: false,
      }).then((templatesResponse) => {
        if (
          templatesResponse.status !== 200 ||
          !templatesResponse.body?.data?.structuredDataObjects?.records
        ) {
          return cy.log(
            'Could not retrieve templates - skipping template delete'
          );
        }

        const templates =
          templatesResponse.body.data.structuredDataObjects.records;

        const targetTemplate = templates.find(
          (template: any) => template.data?.name === templateName
        );

        if (!targetTemplate) {
          return cy.log(
            `Template "${templateName}" not found - nothing to delete (this is expected for clean state)`
          );
        }

        const deleteQuery = `
          mutation deleteSdo {
            deleteStructuredData(input: {
              id: "${targetTemplate.id}"
              schemaId: "${schemaId}"
            }) {
              id
            }
          }
        `;

        cy.request({
          method: 'POST',
          url: `${apiRoot}/v3/graphql`,
          headers: { Authorization: `Bearer ${token}` },
          body: {
            query: deleteQuery,
            variables: {},
          },
          failOnStatusCode: false,
        }).then((deleteResponse) => {
          if (
            deleteResponse.status === 200 &&
            deleteResponse.body?.data?.deleteStructuredData?.id
          ) {
            cy.log(
              `Successfully deleted template "${templateName}" with ID: ${targetTemplate.id}`
            );
            cy.reload();
          } else {
            cy.log(
              `Could not delete template "${templateName}" - this may be expected if it doesn't exist`
            );
          }
          return;
        });

        return;
      });

      return;
    });

    return;
  });
});

// Cypress.Commands.add('delete filter Enity Types', () => {
//   cy.contains('My Cases');
//   cy.get('[data-test="files-tab-button"]', { timeout: 10000 }).click({
//     force: true
//   });
//   cy.contains('FILES');
//   Cy.get('[data-test="top-bar-filters-button"]').click();
//   cy.contains('FILTERS');
//   cy.get('[data-test="filter-section-entity-types"]', { timeout: 5000 })
//     .should('be.visible')
//     .then(() => {
//       cy.get('div:nth-child(1) > label:nth-child(1) [data-test="NONE"]').click({
//         force: true
//       });
//     });
//   cy.get('[data-test="filter-apply-filter"]').click({ force: true });
// });
