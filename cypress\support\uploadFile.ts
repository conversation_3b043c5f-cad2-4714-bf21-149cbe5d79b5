import { uploadProcess } from '../pages/uploadPage';
import {
  categoryIdObjectDetection,
  engineIdMachineboxTagbox,
  categoryIdTranslate,
  engineIdAmazonTranslate,
  categoryIdTranscription,
  engineIdTranscription,
  TestFile,
} from '../fixtures/variables';

Cypress.Commands.add(
  'SelectFile',
  (fileName: string, videoPath: string, mimeType: string) => {
    cy.fixture(videoPath, 'binary', { timeout: 120000 })
      .then(Cypress.Blob.binaryStringToBlob)
      .then((fileContent) => {
        console.log('fileContent', fileContent);
        cy.get('input[type="file"]').attachFile({
          fileContent,
          fileName: fileName,
          mimeType: mimeType,
          encoding: 'utf8',
        });
        return;
      });
  }
);

Cypress.Commands.add('FileUpload', (fileName, videoPath, mimeType) => {
  uploadProcess.openModalUploadFile();
  cy.SelectFile(fileName, videoPath, mimeType);
  cy.get('[data-veritone-element="picker-footer-Upload-button"]').click();
  uploadProcess.editFile(fileName);
});

Cypress.Commands.add(
  'UploadFileBasic',
  (
    fileName: string,
    filePath: string,
    mimeType: string,
    categoryId: string,
    engineId: string
  ) => {
    cy.FileUpload(fileName, filePath, mimeType);
    cy.getDataIdCy({ idAlias: 'next-step' }).click();
    cy.contains('Show Advanced Cognitive Workflow').click();
    uploadProcess.selectAvailableEngine(categoryId, engineId);
    uploadProcess.completeUploadConfigWithoutTemplate();
    cy.contains('created successfully');
  }
);

Cypress.Commands.add(
  'UploadFileAdvanced',
  (fileName: string, filePath: string, mimeType: string, categoryEngines) => {
    cy.FileUpload(fileName, filePath, mimeType);
    cy.getDataIdCy({ idAlias: 'next-step' }).click();
    cy.contains('Show Advanced Cognitive Workflow').click();
    categoryEngines.forEach(
      (categoryEngine: { categoryId: string; engineId: string }) => {
        uploadProcess.selectAvailableEngine(
          categoryEngine.categoryId,
          categoryEngine.engineId
        );
      }
    );
    uploadProcess.saveTemplateEngine();

    cy.get('button').each(($el) => {
      if ($el.text() === 'Override') {
        cy.get('[data-test="confirm-button"]').click();
      }
    });
    uploadProcess.configureUploadAndProceed();
    cy.contains('created successfully');
  }
);

Cypress.Commands.add(
  'UploadFileImageWithEngineFacialDetection',
  (
    fileName: string,
    videoPath: string,
    mimeType: string,
    categoryIdFacialDetection: string,
    engineIdFaceboxSimilarity: string
  ) => {
    cy.FileUpload(fileName, videoPath, mimeType);
    cy.getDataIdCy({ idAlias: 'next-step' }).click();
    uploadProcess.clickSimpleCognitiveWorkflow();
    uploadProcess.clickEngineCategory(categoryIdFacialDetection);
    uploadProcess.clickEngineCategory(categoryIdFacialDetection);
    cy.getDataIdCy({ idAlias: 'show-advanced-cognitive-workflow' }).click();
    uploadProcess.selectAvailableEngine(
      categoryIdFacialDetection,
      engineIdFaceboxSimilarity
    );
    cy.getDataIdCy({ idAlias: 'select-libraries' }).click();
    cy.getDataIdCy({
      idAlias: 'list-libraries_California Known Offenders',
    }).click();
    uploadProcess.configureUploadAndProceed();
  }
);

Cypress.Commands.add(
  'ReprocessBasic',
  (categoryId: string, engineId: string, fileName: string) => {
    cy.contains('FILES').click();
    cy.contains(fileName).parent().prev().click();
    cy.getDataIdCy({ idAlias: 'reprocess-file-icon-button' }).click();
    cy.contains('Show Advanced Cognitive Workflow').click();
    uploadProcess.selectAvailableEngine(categoryId, engineId);
    cy.getDataIdCy({ idAlias: 'save-reprocess' }).click();
    cy.contains('created successfully');
  }
);

Cypress.Commands.add(
  'ReprocessAllWithEngineTranscription',
  (categoryIdTranscription) => {
    cy.getDataIdCy({ idAlias: 'files-table-row' })
      .find('[type="checkbox"]')
      .check();
    cy.getDataIdCy({ idAlias: 'reprocess-file' }).click();
    uploadProcess.clickEngineCategory(categoryIdTranscription);
    cy.getDataIdCy({ idAlias: 'save-reprocess' }).click();
  }
);

Cypress.Commands.add(
  'ReprocessFileVideoWithEngineAmazonTranslate',
  (categoryIdTranslate, engineIdTranslate) => {
    // cy.get('[data-testid^=files-table-row]')
    //   .find('[type="checkbox"]')
    //   .first()
    //   .check();
    cy.getDataIdCy({ idAlias: 'reprocess-file-icon-button' }).click();
    uploadProcess.selectAvailableEngine(categoryIdTranslate, engineIdTranslate);
    uploadProcess.selectLanguages('sw', 'en');
    cy.getDataIdCy({ idAlias: 'save-reprocess' }).click();
    cy.contains('1 jobs are created successfully');
  }
);

Cypress.Commands.add(
  'UploadMultipleFilesBasic',
  (
    files: Array<{
      fileName: string;
      filePath: string;
      mimeType: string;
      categoryId: string;
      engineId: string;
    }>
  ) => {
    if (files.length < 2) {
      throw new Error(
        'At least 2 files are required for multiple file upload test'
      );
    }

    cy.getDataIdCy({ idAlias: 'upload-file' }).click();
    cy.getDataIdCy({ idAlias: 'upload-media' }).click();

    cy.fixture(files[0]!.filePath, 'binary', { timeout: 120000 })
      .then(Cypress.Blob.binaryStringToBlob)
      .then((firstFileContent) => {
        cy.fixture(files[1]!.filePath, 'binary', { timeout: 120000 })
          // eslint-disable-next-line promise/no-nesting
          .then(Cypress.Blob.binaryStringToBlob)
          .then((secondFileContent) => {
            cy.get('input[type="file"]').attachFile([
              {
                fileContent: firstFileContent,
                fileName: files[0]!.fileName,
                mimeType: files[0]!.mimeType,
                encoding: 'utf8',
              },
              {
                fileContent: secondFileContent,
                fileName: files[1]!.fileName,
                mimeType: files[1]!.mimeType,
                encoding: 'utf8',
              },
            ]);
            return;
          });
        return;
      });

    cy.get('[data-veritone-element="picker-footer-Upload-button"]')
      .should('be.enabled')
      .click();

    cy.getDataIdCy({ idAlias: 'stepper' }).should('be.visible');
    cy.contains('Processing').should('be.visible');
    cy.getDataIdCy({ idAlias: 'next-step' }).click();
    cy.quickAdvancedWorkflowSteps(files[0]!.categoryId, files[0]!.engineId);
  }
);

Cypress.Commands.add(
  'quickAdvancedWorkflowSteps',
  (categoryId: string, engineId: string) => {
    cy.contains('Show Advanced Cognitive Workflow').click();
    cy.contains('Advanced Cognitive Workflow').should('be.visible');
    uploadProcess.selectAvailableEngine(categoryId, engineId);
    uploadProcess.completeUploadConfigWithoutTemplate();
    cy.contains('created successfully');
  }
);

Cypress.Commands.add(
  'UploadFileByUrl',
  (imageUrl: string, categoryId: string, engineId: string) => {
    uploadProcess.openModalUploadFile();

    cy.getDataIdCy({ idAlias: 'file-picker-url-tab' }).click();

    cy.get('#url-input').should('be.visible');
    cy.get('#url-input').focus();
    cy.get('#url-input').trigger('paste', {
      clipboardData: {
        getData: () => imageUrl,
      },
    });
    cy.get('img[alt="Uploaded content"]', { timeout: 20000 }).should(
      'be.visible'
    );
    cy.get('[data-veritone-element="picker-footer-Upload-button"]').click();
    cy.getDataIdCy({ idAlias: 'next-step' }).click();
    cy.quickAdvancedWorkflowSteps(categoryId, engineId);
  }
);

Cypress.Commands.add('AddMoreFilesWithPlusIcon', () => {
  uploadProcess.openModalUploadFile();

  cy.SelectFile(
    TestFile.ImagePlantJpeg.name,
    TestFile.ImagePlantJpeg.path,
    TestFile.ImagePlantJpeg.type
  );
  cy.get('[data-veritone-element="picker-footer-Upload-button"]').click();

  cy.contains('1 files').should('be.visible');
  cy.getDataIdCy({ idAlias: 'add-file' }).should('be.visible').click();

  cy.SelectFile(
    TestFile.E2eAudio.name,
    TestFile.E2eAudio.path,
    TestFile.E2eAudio.type
  );
  cy.get('[data-veritone-element="picker-footer-Upload-button"]').click();

  cy.contains('2 files').should('be.visible');

  cy.getDataIdCy({ idAlias: 'checked-all-file' })
    .find('[type="checkbox"]')
    .check();
  cy.get('[data-test="edit-file"]').click();
  cy.get('[data-test="file-name-upload"] input').clear();
  cy.get('[data-test="file-name-upload"] input').type('test-multiple-files');
  cy.getDataIdCy({ idAlias: 'tags-edit' }).click();
  cy.get('[data-test="tags-upload"] input').type('demo{enter}');
  cy.contains('General').click();
  cy.getDataIdCy({ idAlias: 'save-edit-file' }).click();

  cy.getDataIdCy({ idAlias: 'next-step' }).click();
  cy.quickAdvancedWorkflowSteps(
    categoryIdObjectDetection,
    engineIdMachineboxTagbox
  );
});

Cypress.Commands.add('EditSelectedFileInUploadScreen', () => {
  uploadProcess.openModalUploadFile();

  cy.SelectFile(
    TestFile.ImagePlantJpeg.name,
    TestFile.ImagePlantJpeg.path,
    TestFile.ImagePlantJpeg.type
  );
  cy.get('[data-veritone-element="picker-footer-Upload-button"]').click();

  cy.getDataIdCy({ idAlias: 'list-file', options: { timeout: 30000 } }).should(
    'be.visible'
  );
  cy.getDataIdCy({ idAlias: 'checkbox-file' }).should('be.visible').click();
  cy.get('[data-test="edit-file"]')
    .should('be.visible')
    .should('not.be.disabled')
    .click();

  cy.contains('Edit Media').should('be.visible');
  const newFileName = 'edited-test-file';
  cy.get('[data-test="file-name-upload"] input').should('be.visible').clear();
  cy.get('[data-test="file-name-upload"] input').type(newFileName);
  cy.getDataIdCy({ idAlias: 'tags-edit' }).click();
  cy.get('[data-test="tags-upload"] input').type('demo{enter}');
  cy.contains('General').click();
  cy.getDataIdCy({ idAlias: 'save-edit-file' }).click();
  cy.getDataIdCy({ idAlias: 'list-file', options: { timeout: 30000 } }).should(
    'be.visible'
  );
  cy.getDataIdCy({ idAlias: 'next-step' }).click();
  cy.quickAdvancedWorkflowSteps(
    categoryIdObjectDetection,
    engineIdMachineboxTagbox
  );
});

Cypress.Commands.add('DeleteSelectedFileInUploadScreen', () => {
  uploadProcess.openModalUploadFile();

  cy.SelectFile(
    TestFile.ImagePlantJpeg.name,
    TestFile.ImagePlantJpeg.path,
    TestFile.ImagePlantJpeg.type
  );
  cy.get('[data-veritone-element="picker-footer-Upload-button"]').click();
  cy.getDataIdCy({ idAlias: 'list-file', options: { timeout: 30000 } }).should(
    'be.visible'
  );
  cy.getDataIdCy({ idAlias: 'add-file' }).should('be.visible').click();

  cy.SelectFile(
    TestFile.E2eAudio.name,
    TestFile.E2eAudio.path,
    TestFile.E2eAudio.type
  );
  cy.get('[data-veritone-element="picker-footer-Upload-button"]').click();
  cy.getDataIdCy({ idAlias: 'checkbox-file' })
    .first()
    .should('be.visible')
    .click();
  cy.getDataIdCy({ idAlias: 'remove-file' })
    .should('be.visible')
    .should('not.be.disabled')
    .click();
  cy.getDataIdCy({ idAlias: 'list-file', options: { timeout: 30000 } }).should(
    'be.visible'
  );
  cy.getDataIdCy({ idAlias: 'next-step' }).click();
  cy.contains('Processing').should('be.visible');
});

Cypress.Commands.add('UploadFileWithoutEngines', () => {
  uploadProcess.openModalUploadFile();

  cy.SelectFile(
    TestFile.ImagePlantJpeg.name,
    TestFile.ImagePlantJpeg.path,
    TestFile.ImagePlantJpeg.type
  );
  cy.get('[data-veritone-element="picker-footer-Upload-button"]').click();
  uploadProcess.editFile(TestFile.ImagePlantJpeg.name);
  cy.getDataIdCy({ idAlias: 'next-step' }).click();
  cy.contains('Simple Cognitive Workflow').should('be.visible');
  cy.contains('Show Advanced Cognitive Workflow').click();
  cy.contains('Advanced Cognitive Workflow').should('be.visible');
  uploadProcess.completeUploadConfigWithoutTemplate();
  cy.contains('created successfully');
});

Cypress.Commands.add(
  'UploadAudioFileWithTranslationRequiringTranscription',
  () => {
    uploadProcess.openModalUploadFile();

    cy.SelectFile(
      TestFile.E2eAudio.name,
      TestFile.E2eAudio.path,
      TestFile.E2eAudio.type
    );
    cy.get('[data-veritone-element="picker-footer-Upload-button"]').click();
    uploadProcess.editFile(TestFile.E2eAudio.name);
    cy.getDataIdCy({ idAlias: 'next-step' }).click();

    cy.contains('Show Advanced Cognitive Workflow').click();
    cy.contains('Advanced Cognitive Workflow').should('be.visible');

    uploadProcess.selectAvailableEngine(
      categoryIdTranslate,
      engineIdAmazonTranslate
    );
    uploadProcess.completeUploadConfigWithoutTemplate();
    cy.screenshot('after-trying-to-save-translation-only');

    cy.get('body', { timeout: 20000 }).then(($body) => {
      const bodyText = $body.text();

      if (bodyText.includes('error') || bodyText.includes('Error')) {
        cy.log('Found error message on page');

        if (
          bodyText.includes('transcription') ||
          bodyText.includes('Transcription')
        ) {
          cy.log('Found transcription-related error message');
        }

        if (bodyText.includes('translate') || bodyText.includes('Translate')) {
          cy.log('Found translation-related error message');
        }
      } else if (bodyText.includes('created successfully')) {
        cy.log(
          'Upload completed successfully - system handled transcription dependency'
        );
      } else {
        cy.log('Checking current page state for debugging');
      }

      return;
    });
  }
);

Cypress.Commands.add('EditMediaFileBeforeCompletingUpload', () => {
  uploadProcess.openModalUploadFile();

  cy.SelectFile(
    TestFile.ImagePlantJpeg.name,
    TestFile.ImagePlantJpeg.path,
    TestFile.ImagePlantJpeg.type
  );
  cy.get('[data-veritone-element="picker-footer-Upload-button"]').click();

  cy.getDataIdCy({ idAlias: 'checkbox-file' }).should('be.visible').click();

  cy.get('[data-test="edit-file"]')
    .should('be.visible')
    .should('not.be.disabled')
    .click();

  cy.contains('Edit Media').should('be.visible');

  const newFileName = 'edited-media-file-before-upload';
  const newDescription = 'This file was edited before upload';

  cy.get('[data-test="file-name-upload"] input').should('be.visible');
  cy.get('[data-test="file-name-upload"] input').clear();
  cy.get('[data-test="file-name-upload"] input').type(newFileName);

  cy.get('body').then(($body) => {
    if ($body.find('[data-test="file-description-upload"]').length > 0) {
      cy.get('[data-test="file-description-upload"] textarea').clear();
      cy.get('[data-test="file-description-upload"] textarea').type(
        newDescription
      );
    }
    return;
  });

  cy.getDataIdCy({ idAlias: 'tags-edit' }).click();
  cy.get('[data-test="tags-upload"] input').type('demo{enter}');
  cy.contains('General').click();
  cy.getDataIdCy({ idAlias: 'save-edit-file' }).click();
  cy.getDataIdCy({ idAlias: 'list-file', options: { timeout: 30000 } }).should(
    'be.visible'
  );
  cy.getDataIdCy({ idAlias: 'next-step' }).click();
  cy.quickAdvancedWorkflowSteps(
    categoryIdObjectDetection,
    engineIdMachineboxTagbox
  );
});

Cypress.Commands.add('AddTagsInEditMediaBeforeUpload', () => {
  uploadProcess.openModalUploadFile();

  cy.SelectFile(
    TestFile.ImagePlantJpeg.name,
    TestFile.ImagePlantJpeg.path,
    TestFile.ImagePlantJpeg.type
  );
  cy.get('[data-veritone-element="picker-footer-Upload-button"]').click();

  cy.getDataIdCy({ idAlias: 'checkbox-file' }).should('be.visible').click();
  cy.get('[data-test="edit-file"]')
    .should('be.visible')
    .should('not.be.disabled')
    .click();
  cy.contains('Edit Media').should('be.visible');
  cy.getDataIdCy({ idAlias: 'tags-edit' }).should('be.visible').click();
  const tagsToAdd = ['test-tag-1', 'media-edit-tag', 'before-upload-tag'];

  tagsToAdd.forEach((tag, index) => {
    cy.log(`Adding tag ${index + 1}: ${tag}`);
    cy.getDataIdCy({ idAlias: 'input-tags' }).type(tag);
    cy.getDataIdCy({ idAlias: 'add-tag' }).click();
    cy.getDataIdCy({ idAlias: 'chip' }).contains(tag).should('be.visible');
  });
  cy.log('Verifying all tags were added successfully');
  tagsToAdd.forEach((tag) => {
    cy.getDataIdCy({ idAlias: 'chip' }).contains(tag).should('be.visible');
  });

  cy.getDataIdCy({ idAlias: 'general-edit' }).click();
  cy.contains('General').click();
  cy.getDataIdCy({ idAlias: 'save-edit-file' }).click();
  cy.getDataIdCy({ idAlias: 'list-file', options: { timeout: 30000 } }).should(
    'be.visible'
  );
  cy.getDataIdCy({ idAlias: 'next-step' }).click();
  cy.quickAdvancedWorkflowSteps(
    categoryIdObjectDetection,
    engineIdMachineboxTagbox
  );
  cy.log('Successfully added tags in edit media before completing upload');
});

Cypress.Commands.add('UploadMediaFileWithoutAudioAndRunsTranscription', () => {
  uploadProcess.openModalUploadFile();

  cy.SelectFile(
    TestFile.E2eVideoNoAudio.name,
    TestFile.E2eVideoNoAudio.path,
    TestFile.E2eVideoNoAudio.type
  );
  cy.get('[data-veritone-element="picker-footer-Upload-button"]').click();
  uploadProcess.editFile(TestFile.E2eVideoNoAudio.name);
  cy.getDataIdCy({ idAlias: 'next-step' }).click();
  cy.quickAdvancedWorkflowSteps(categoryIdTranscription, engineIdTranscription);
  cy.log(
    'Successfully uploaded video file without audio and ran transcription engine'
  );
});
