/// <reference types="cypress" />

describe('Debug Navigation to Upload Folder', () => {
  beforeEach(() => {
    cy.LoginLandingPage();
  });

  it('should list all available folders for debugging', () => {
    cy.log('🧪 Starting folder discovery test');
    cy.listAvailableFolders();
    cy.log('✅ Folder discovery completed');
  });

  it('should navigate to upload folder with detailed logging', () => {
    cy.log('🧪 Starting navigation test');

    // Test the navigation command
    cy.NavigateToUploadFolder();

    // Verify we're in the right place by checking breadcrumbs or other indicators
    cy.log('🔍 Verifying navigation success');

    // Wait a bit to see the final state
    cy.wait(2000);

    cy.log('✅ Navigation test completed');
  });

  it('should test robust navigation', () => {
    cy.log('🧪 Starting robust navigation test');
    cy.NavigateToUploadFolderRobust();
    cy.wait(2000);
    cy.log('✅ Robust navigation test completed');
  });

  it('should test file deletion with detailed logging', () => {
    cy.log('🧪 Starting file deletion test');

    // First navigate to the folder
    cy.NavigateToUploadFolder();

    // Test deletion of a single file
    const testFile = 'e2e/upload/image-plant-jpeg.jpeg';
    cy.log(`🗑️ Testing deletion of: ${testFile}`);

    // Import the uploadPage object
    cy.then(() => {
      const { uploadPage } = require('../../pages/uploadPage');
      uploadPage.deleteFileByName(testFile);
    });

    cy.wait(3000); // Give time for deletion to complete

    cy.log('✅ File deletion test completed');
  });

  it('should test the full upload file feature flow', () => {
    cy.log('🧪 Testing full upload file feature flow');

    // Simulate the exact same flow as the feature file
    const files = [
      { filename: 'e2e/upload/image-plant-jpeg.jpeg' },
      { filename: 'e2e/upload/spanish-pdf.pdf' },
      { filename: 'e2e/upload/spanish-email.eml' },
      { filename: 'e2e/upload/spanish-txt.txt' },
      { filename: 'e2e/upload/edited-media-file-before-upload' },
      { filename: 'e2e/upload/edited-test-file' },
      { filename: 'e2e/upload/test-multiple-files' },
      { filename: 'e2e/upload/200.jpg' },
      { filename: 'e2e/upload/e2e_audio.mp3' }
    ];

    cy.log(`🧹 Starting cleanup process for ${files.length} files`);
    cy.log(`Files to delete: ${files.map((f) => f.filename).join(', ')}`);

    // Check environment variables
    cy.log(`Environment check:`);
    cy.log(`- API Root: ${Cypress.env('apiRoot')}`);
    cy.log(`- Token available: ${!!Cypress.env('token')}`);

    files.forEach(({ filename }, index) => {
      if (!filename) {
        throw new Error('Filename is required in DataTable');
      }
      cy.log(`🗑️ [${index + 1}/${files.length}] Processing deletion for: ${filename}`);

      cy.then(() => {
        const { uploadPage } = require('../../pages/uploadPage');
        uploadPage.deleteFileByName(filename);
      });
    });

    cy.log(`📁 Navigating to upload folder...`);
    cy.NavigateToUploadFolder();
    cy.log(`✅ Navigation to upload folder completed`);

    cy.log('✅ Full flow test completed');
  });
});
