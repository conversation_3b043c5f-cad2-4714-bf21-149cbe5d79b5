Feature: Tab Exports

  Background:
    Given The user navigates to the tab "EXPORTS"

  @e2e @tabExports
  Scenario: The user verifies the content of the Exports tab
    Then The "EXPORTS" tab should display the total number of files
    And The exports table should have the following columns:
      | Name           |
      | Status         |
      | Date Initiated |
      | Actions        |
    And The table should have pagination controls

  @e2e @tabExports
  Scenario: The user verifies the default name format of the export file
    Then Each export file name in the list should follow the default format

  @e2e @tabExports
  Scenario: The user verifies the status of the export files
    Then Each export file in the list should have a "complete" status

  @e2e @tabExports
  Scenario: The user verifies the date initiated format
    Then The date initiated for each export file should follow the correct format

  @e2e @tabExports
  Scenario: The user clicks the file download button
    When The user clicks the download button for an export file

  @e2e @tabExports
  Scenario: The user can change the number of rows displayed per page
    Given The rows per page selector defaults to "10"
    Then The user verifies changing the rows per page for the following values:
      |  20 |
      | 100 |
      |  10 |

  @e2e @tabExports
  Scenario: The user navigates through the pages
    Then The pagination controls for the "first" page are correct
    When The user clicks the "Next" page button
    Then The pagination controls for the "second" page are correct
    When The user clicks the "Previous" page button
    Then The pagination controls for the "first" page are correct
