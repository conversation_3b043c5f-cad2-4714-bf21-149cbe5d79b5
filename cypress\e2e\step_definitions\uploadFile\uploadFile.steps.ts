import {
  Before,
  DataTable,
  Then,
  When,
} from '@badeball/cypress-cucumber-preprocessor';
import { uploadPage } from '../../../pages/uploadPage';

Before(() => {
  cy.LoginLandingPage();
});

When('Navigate to upload folder and delete file:', (fileList: DataTable) => {
  const files = fileList.hashes();

  cy.log(
    `Starting deletion of files: ${files.map((f) => f.filename).join(', ')}`
  );
  files.forEach(({ filename }) => {
    if (!filename) {
      throw new Error('Filename is required in DataTable');
    }
    cy.log(`Deleting file: ${filename}`);
    uploadPage.deleteFileByName(filename);
  });

  cy.NavigateToUploadFolder();
});

When('The user opens the upload screen', () => {
  uploadPage.clickUploadButton();
});

Then('The screen is displayed correctly with all default elements', () => {
  uploadPage.verifyInitialUploadScreen();
});

When('The user upload image file for object detection', () => {
  uploadPage.uploadImage();
});

When('The user uploads multiple files for processing', () => {
  uploadPage.uploadMultipleFiles();
});

When('Upload pdf file for text extraction', () => {
  uploadPage.uploadPDFFile();
});

When('Upload eml file for text extraction', () => {
  uploadPage.uploadEMLFile();
});

When('Upload video file for transcription', () => {
  uploadPage.uploadVideoFile();
});

When('Upload audio for transcription', () => {
  uploadPage.uploadAudioFile();
});

When('Upload text file for spanish to english translation', () => {
  uploadPage.UploadTextFile();
});

When('The user uploads a file by browse', () => {
  uploadPage.uploadFileBrowse();
});

When('The user uploads an image by URL', () => {
  uploadPage.uploadImageByUrl();
});

When('The user adds more files using the plus icon', () => {
  uploadPage.addMoreFilesUsingPlusIcon();
});

When('The user edits a selected file in upload screen', () => {
  uploadPage.editSelectedFileInUploadScreen();
});

When('The user deletes a selected file in upload screen', () => {
  uploadPage.deleteSelectedFileInUploadScreen();
});

When('The user uploads a file without running any engines', () => {
  uploadPage.uploadFileWithoutEngines();
});

When(
  'The user uploads audio file with translation that requires transcription',
  () => {
    uploadPage.uploadAudioFileWithTranslationRequiringTranscription();
  }
);

When('The user edits media file before completing upload', () => {
  uploadPage.editMediaFileBeforeCompletingUpload();
});

When('The user adds tags in edit media before upload', () => {
  uploadPage.addTagsInEditMediaBeforeUpload();
});

When('The user closes the upload screen', () => {
  uploadPage.closeUploadScreen();
});

When('The user uploads media file without audio and runs transcription', () => {
  uploadPage.uploadMediaFileWithoutAudioAndRunsTranscription();
});

Then('The console should not log any errors', () => {
  uploadPage.verifyNoConsoleErrors();
});
