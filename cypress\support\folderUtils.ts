/// <reference types="cypress" />

/**
 * Utility functions for folder operations in Cypress tests
 */

export const folderUtils = {
  /**
   * Ensures the upload folder exists, creates it if it doesn't
   */
  ensureUploadFolderExists: (): void => {
    cy.log('🔍 Checking if upload folder exists...');
    
    // Navigate to folder selection
    cy.get('[data-test="top-bar-select-folder"]').click({ force: true });
    
    cy.get('[data-test="folder-modal-dialog-content"]', { timeout: 10000 })
      .should('be.visible')
      .then(($dialog) => {
        // Check if e2e folder exists
        if ($dialog.find('*:contains("e2e")').length > 0) {
          cy.log('✅ e2e folder found');
          cy.wrap($dialog).contains('e2e').click();
          
          // Check if upload subfolder exists
          cy.get('[data-test="folder-modal-dialog-content"]').then(($updatedDialog) => {
            if ($updatedDialog.find('*:contains("upload")').length > 0) {
              cy.log('✅ upload folder found');
              cy.wrap($updatedDialog).contains('upload').click();
              cy.get('[data-test=folder-modal-select-folder-button]').click();
            } else {
              cy.log('❌ upload folder not found, creating it...');
              folderUtils.createUploadFolder();
            }
          });
        } else {
          cy.log('❌ e2e folder not found, creating folder structure...');
          folderUtils.createFolderStructure();
        }
      });
  },

  /**
   * Creates the upload folder under e2e
   */
  createUploadFolder: (): void => {
    cy.log('🏗️ Creating upload folder...');
    
    // Look for create folder button or option
    cy.get('body').then(($body) => {
      if ($body.find('[data-test="create-folder-button"]').length > 0) {
        cy.get('[data-test="create-folder-button"]').click();
        cy.get('[data-test="folder-name-input"]').type('upload');
        cy.get('[data-test="confirm-create-folder"]').click();
      } else {
        cy.log('❌ Cannot find create folder button, using fallback...');
        // Close dialog and use current folder
        cy.get('[data-test=folder-modal-select-folder-button]').click();
      }
    });
  },

  /**
   * Creates the entire e2e/upload folder structure
   */
  createFolderStructure: (): void => {
    cy.log('🏗️ Creating e2e/upload folder structure...');
    
    // This would need to be implemented based on the specific UI
    // For now, just close the dialog and continue
    cy.get('[data-test=folder-modal-select-folder-button]').click();
  },

  /**
   * Lists all available folders for debugging
   */
  listAvailableFolders: (): void => {
    cy.get('[data-test="top-bar-select-folder"]').click({ force: true });
    
    cy.get('[data-test="folder-modal-dialog-content"]', { timeout: 10000 })
      .should('be.visible')
      .then(($dialog) => {
        const folderTexts = [];
        $dialog.find('*').each((index, element) => {
          const text = Cypress.$(element).text().trim();
          if (text && text.length > 0 && text.length < 50 && !text.includes('Select') && !text.includes('Cancel')) {
            folderTexts.push(text);
          }
        });
        cy.log(`📁 Available folders: ${[...new Set(folderTexts)].join(', ')}`);
        
        // Close the dialog
        cy.get('[data-test="folder-modal-cancel-folder-button"]').click();
      });
  }
};

// Add Cypress commands
Cypress.Commands.add('ensureUploadFolderExists', folderUtils.ensureUploadFolderExists);
Cypress.Commands.add('listAvailableFolders', folderUtils.listAvailableFolders);
