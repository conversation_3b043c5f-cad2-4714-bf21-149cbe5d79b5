import {
  Before,
  DataTable,
  Given,
  Then,
  When,
} from '@badeball/cypress-cucumber-preprocessor';
import { mediaListPage } from '../../../pages/mediaListPage';
import { exportPage } from '../../../pages/exportPage';

Before(() => {
  cy.LoginLandingPage();
});

Given('The user is on the media list page', () => {
  mediaListPage.goToMediaListPage();
});

When('The user exports a file', () => {
  mediaListPage.exportFile();
});

When('The user exports all items', () => {
  mediaListPage.exportAllItems();
});

When('The user selects the file {string}', (fileName: string) => {
  exportPage.selectFile(fileName);
});

When('The user clicks the {string} toolbar button', (buttonName: string) => {
  exportPage.clickToolbarButton(buttonName);
});

Then('The "Advanced Export" pop-up is displayed', () => {
  exportPage.verifyAdvancedExportPopupDisplayed();
});

Then('The {string} tab should be selected', (tabName: string) => {
  exportPage.verifyTabSelected(tabName);
});

Then(
  'The following export options should be visible:',
  (dataTable: DataTable) => {
    const options = dataTable
      .rows()
      .map((row: string[]) => row[0])
      .filter(Boolean) as string[];

    exportPage.verifyExportOptionsVisible(options);
  }
);

Then(
  'The following buttons should be visible on the export pop-up:',
  (dataTable: DataTable) => {
    const buttons = dataTable
      .rows()
      .map((row: string[]) => row[0])
      .filter(Boolean) as string[];

    exportPage.verifyButtonsVisibleOnExportPopup(buttons);
  }
);

Then('The {string} export option should be checked', (optionName: string) => {
  exportPage.verifyExportOptionChecked(optionName);
});

Then('The {string} export option should be disabled', (optionName: string) => {
  exportPage.verifyExportOptionDisabled(optionName);
});

When('The user checks the {string} export option', (optionName: string) => {
  exportPage.checkExportOption(optionName);
});

When('The user unchecks the {string} export option', (optionName: string) => {
  exportPage.uncheckExportOption(optionName);
});

Then(
  'The {string} export option should not be checked',
  (optionName: string) => {
    exportPage.verifyExportOptionNotChecked(optionName);
  }
);

When(
  'The user clicks the edit icon for the {string} field',
  (fieldName: string) => {
    exportPage.clickEditIconForField(fieldName);
  }
);

When(
  'The user renames the field {string} to {string}',
  (originalName: string, newName: string) => {
    exportPage.renameField(originalName, newName);
  }
);

When(
  'The user clicks the {string} button in the dialog',
  (buttonName: string) => {
    exportPage.clickDialogButton(buttonName);
  }
);

Then(
  'The input for {string} should have the value {string}',
  (labelName: string, expectedValue: string) => {
    exportPage.verifyInputValue(labelName, expectedValue);
  }
);

When(
  'The user clicks the {string} button on the export pop-up',
  (buttonName: string) => {
    exportPage.clickExportPopupButton(buttonName);
  }
);

Then('The {string} dialog should appear', (name: string) => {
  exportPage.verifyDialogAppears(name);
});

When('The user names the template {string}', (templateName: string) => {
  exportPage.nameTemplate(templateName);
});

When(
  'The user clicks the {string} button in the new template dialog',
  (buttonName: string) => {
    exportPage.clickNewTemplateDialogButton(buttonName);
  }
);

Then('A success message {string} is displayed', (message: string) => {
  exportPage.verifySuccessMessage(message);
});

Given(
  'The user deletes the export template {string}',
  (templateName: string) => {
    cy.deleteExportTemplate(templateName);
  }
);

When(
  'The user checks the following export options:',
  (dataTable: DataTable) => {
    const options = dataTable
      .rows()
      .map((row: string[]) => row[0])
      .filter(Boolean) as string[];
    exportPage.checkMultipleExportOptions(options);
  }
);

When(
  'The user unchecks the following export options:',
  (dataTable: DataTable) => {
    const options = dataTable
      .rows()
      .map((row: string[]) => row[0])
      .filter(Boolean) as string[];
    exportPage.uncheckMultipleExportOptions(options);
  }
);

When(
  'The user selects the export template {string}',
  (templateName: string) => {
    exportPage.selectExportTemplate(templateName);
  }
);

Then(
  'The following export options should be checked:',
  (dataTable: DataTable) => {
    const options = dataTable
      .rows()
      .map((row: string[]) => row[0])
      .filter(Boolean) as string[];
    exportPage.verifyMultipleExportOptionsChecked(options);
  }
);

When('The user navigates to the {string} tab', (tabName: string) => {
  exportPage.navigateToTab(tabName);
});

When('The user clears the Archive Name field', () => {
  exportPage.clearArchiveNameField();
});

Then(
  'The Archive Name field should show the error message {string}',
  (errorMessage: string) => {
    exportPage.verifyArchiveNameErrorMessage(errorMessage);
  }
);

Then('The "Export" button on the export pop-up should be disabled', () => {
  exportPage.verifyExportButtonDisabled();
});

When('The user sets the Archive Name to {string}', (archiveName: string) => {
  exportPage.setArchiveName(archiveName);
});

Then('The Archive Name field should not show an error message', () => {
  exportPage.verifyArchiveNameNoErrorMessage();
});

Then('The "Export" button on the export pop-up should be enabled', () => {
  exportPage.verifyExportButtonEnabled();
});

When(
  'The user clicks the {string} button in option tab',
  (buttonName: string) => {
    exportPage.clickOptionTabButton(buttonName);
  }
);

Then('The "Set Password" dialog of option should appear', () => {
  exportPage.verifySetPasswordDialogAppears();
});

When('The user enters the password {string}', (password: string) => {
  exportPage.enterPassword(password);
});

When(
  'The user clicks the {string} button in the password dialog',
  (buttonName: string) => {
    exportPage.clickPasswordDialogButton(buttonName);
  }
);

Then('The "Set Password" dialog should disappear', () => {
  exportPage.verifySetPasswordDialogDisappears();
});

When('The user opens the notifications panel', () => {
  exportPage.openNotificationsPanel();
});

When(
  'The user downloads the first completed export from the notifications panel',
  () => {
    exportPage.downloadFirstCompletedExportFromNotifications();
  }
);

When('The user closes the notifications panel', () => {
  exportPage.closeNotificationsPanel();
});

When(
  'The user navigates to the main application tab {string}',
  (tabName: string) => {
    exportPage.navigateToMainApplicationTab(tabName);
  }
);

Then(
  'The user can download the first completed export from the exports table',
  () => {
    exportPage.verifyCanDownloadFirstCompletedExport();
  }
);
