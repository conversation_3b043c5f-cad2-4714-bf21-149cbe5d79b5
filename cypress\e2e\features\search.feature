Feature: Search

  @e2e @search
  Scenario: Verify UI of search bar
    When The user clicks on the search bar
    Then The search bar pop-up is displayed
    And The pop-up contains a list of searchable options
    And The default focus is on "Search by Keyword"
    And The text "Search by keyword within our database of transcripts." is displayed
    And A text box to input phrase to search exists with placeholder "Phrase to search"
    And The "Close" and "Add" buttons are displayed
    When The user clicks the "Close" button on the search bar pop-up
    Then The search bar pop-up is no longer visible

  @e2e @search
  Scenario: Search by Keyword
    When The user searches by keyword "Welcome" to find file "e2e_audio.mp3"

  @e2e @search
  Scenario: Search by tag
    When The user searches by tag "tagSearch" to find file "bloomberg.mp4"

  @e2e @search
  Scenario: Verify user can search all results except keywords when using search by Tag
    When The user searches by tag "tagSearch" to find file "e2e_video.mp4" with exclude option

  @e2e @search
  Scenario: Search by bookmark
    When The user searches by bookmark "bookmarkSear" to find file "e2e_video.mp4"

  @e2e @search_keyword
  Sc<PERSON><PERSON>: Verify user search for a keyword
    When The user adds the following search criteria:
      | type    | value    |
      | Keyword | Facebook |
    Then The file list is displayed
    When The user clicks on the file "e2e_audio.mp3"
    Then The media details page is displayed
    And The transcript contains the following highlighted keywords:
      | keyword  |
      | Facebook |

  @e2e @search_multiple_keywords
  Scenario: Verify user can search for multiple keywords in the same tdo at the same time
    When The user adds the following search criteria:
      | type    | value    |
      | Keyword | Facebook |
      | Keyword | Apple    |
    Then The file list is displayed
    When The user clicks on the file "e2e_audio.mp3"
    Then The media details page is displayed
    And The transcript contains the following highlighted keywords:
      | keyword  |
      | Facebook |
      | Apple    |
    And The transcript focuses on the first highlighted keyword "Facebook"

  @e2e @search
  Scenario: Verify user can search by Recognized Text
    When The user adds the following search criteria:
      | type            | value     |
      | Recognized Text | Singapore |
    Then The file "e2e_video.mp4" is displayed in the list
    When The user clicks on the file "e2e_video.mp4"
    Then The media details page is displayed
    When The user selects the "Text Recognition" category
    And The OCR results contain the highlighted text "Singapore"

  @e2e @search
  Scenario: Verify user can search by recognize text including special characters
    When The user adds the following search criteria:
      | type            | value                              |
      | Recognized Text | IS THE WORST OVER FOR COMMODITIES? |
    Then The file "e2e_video.mp4" is displayed in the list
    When The user clicks on the file "e2e_video.mp4"
    Then The media details page is displayed
    When The user selects the "Text Recognition" category
    And The OCR results contain the highlighted text "IS THE WORST OVER FOR COMMODITIES?"

  @e2e @search
  Scenario: Verify user can remove an applied search filter
    When The user adds the following search criteria:
      | type    | value    |
      | Keyword | Facebook |
    Then The file "e2e_audio.mp3" is displayed in the list
    And The file "bloomberg.mp4" is not displayed in the list
    When The user removes the search for "Facebook"
    Then The file "e2e_audio.mp3" is displayed in the list
    And The file "bloomberg.mp4" is displayed in the list

  @e2e @search
  Scenario: Verify search with no results
    When The user adds the following search criteria:
      | type    | value               |
      | Keyword | keywordDoesNotExist |
    Then The file list should display "No data"

  @e2e @search @skip
  Scenario: Verify user can search by Text

  @e2e @search @skip
  Scenario: Verify user can search by Face

  @e2e @search @skip
  Scenario: Verify user can search by Object

  @e2e @search @skip
  Scenario: Verify user can search by Sentinment

  @e2e @search @skip
  Scenario: Verify user can search by Geolocation

  @e2e @search
  Scenario: Verify Search by Time
    When The user searches by time from "01:00" to "16:00"
    Then The file "e2e_video.mp4" is displayed in the list
