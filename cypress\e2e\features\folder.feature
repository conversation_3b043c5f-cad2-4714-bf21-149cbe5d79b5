Feature: Folder

  @e2e @folder
  Scenario: Verify user can see folder list
    When The user clicks the folder icon
    Then The folder list dialog is displayed
    And The "MY CASES" root folder is displayed with a counter
    When The user closes the folder dialog using the "X" button
    Then The folder list dialog is no longer visible
    When The user clicks the folder icon
    When The user closes the folder dialog using the "Cancel" button
    Then The folder list dialog is no longer visible

  @e2e @folder
  Scenario: Verify user can hover over folder to view tooltip
    Given The user has opened the folder selection window
    When The user hovers over the "MY CASES" folder
    Then The folder tooltip should display with the correct text

  @e2e @folder
  Scenario: Verify user can create new folder in My cases
    Given The user deletes folder "e2e_test_folder"
    When The user creates the "e2e_test_folder" in the "My Cases" parent directory

  @e2e @folder
  Scenario: Verify user can select a folder
    Given The user has opened the folder selection window
    When The user selects the "e2e_test_folder" folder
    Then The "e2e_test_folder" breadcrumb should be visible

  @e2e @folder
  Scenario: Verify user can create folder with long name
    Given The user deletes folder "ThisIsAVeryLongFolderNameForTestingToEnsureTheSystemHandlesItGracefullyWithoutAnyErrorsOrTruncation"
    When The user creates the "ThisIsAVeryLongFolderNameForTestingToEnsureTheSystemHandlesItGracefullyWithoutAnyErrorsOrTruncation" in the "My Cases" parent directory

  @e2e @folder
  Scenario: Verify user can create a child folder
    Given The user deletes folder "e2e_test_folder/child_folder"
    And The user deletes folder "renamed_child_folder"
    When The user creates the "child_folder" in the "e2e_test_folder" parent directory

  @e2e @folder
  Scenario: Verify user can rename folder
    Given The user deletes folder "originalName"
    Given The user deletes folder "RenamedFolder"
    When The user creates the "originalName" in the "My Cases" parent directory
    When The user renames the folder "originalName" to "RenamedFolder" in the "My Cases" parent directory

  @e2e @folder
  Scenario: Verify user can rename for inner folder
    Given The user deletes folder "renamed_child_folder"
    When The user renames the folder "child_folder" to "renamed_child_folder" in the "e2e_test_folder" parent directory

  @e2e @folder
  Scenario: Verify user can move a folder into another folder
    When The user moves the folder "renamed_child_folder" from "e2e_test_folder" to "My Cases"

  @e2e @folder
  Scenario: Verify user can move a child folder to become a parent folder
    Given The user deletes folder "temp_parent"
    When The user creates the "temp_parent" in the "My Cases" parent directory
    And The user creates the "temp_child" in the "temp_parent" parent directory
    And The user moves the folder "temp_child" from "temp_parent" to "My Cases"
