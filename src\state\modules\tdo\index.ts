import fetchGraphQLApi from '../../../helpers/fetchGraphQLApi';
import { getFolderTreeObjectId } from '../folders/folder';

export async function deleteTDOInFolder(
  endpoint: string,
  token: string,
  veritoneAppId: string,
  folderName: string
) {
  if (!folderName) {
    return [];
  }
  const tdos = await getTdosByFolder(
    endpoint,
    token,
    veritoneAppId,
    folderName
  );
  const tdoIds = tdos.map((tdo) => tdo.recording.recordingId);
  return await deleteTDO(endpoint, token, veritoneAppId, tdoIds);
}

export async function deleteTDOByFilePath(
  endpoint: string,
  token: string,
  veritoneAppId: string,
  filePath: string
) {
  console.log(`[deleteTDOByFilePath] Starting deletion for: ${filePath}`);

  if (!filePath) {
    console.log(`[deleteTDOByFilePath] Empty file path provided`);
    return [];
  }

  console.log(`[deleteTDOByFilePath] Searching for TDO with path: ${filePath}`);
  const tdo = await getTdoByFilePath(endpoint, token, veritoneAppId, filePath);

  if (!tdo) {
    console.log(`[deleteTDOByFilePath] No TDO found for path: ${filePath}`);
    return [];
  }

  if (!tdo?.recording?.recordingId) {
    console.log(`[deleteTDOByFilePath] TDO found but no recording ID for path: ${filePath}`, tdo);
    return [];
  }

  const tdoIds = [tdo.recording.recordingId];
  console.log(`[deleteTDOByFilePath] Deleting TDO with ID: ${tdoIds[0]} for path: ${filePath}`);

  const result = await deleteTDO(endpoint, token, veritoneAppId, tdoIds);
  console.log(`[deleteTDOByFilePath] Deletion result for ${filePath}:`, result);

  return result;
}

export async function getTdoByFilePath(
  endpoint: string,
  token: string,
  veritoneAppId: string,
  filePath: string
) {
  console.log(`[getTdoByFilePath] Searching for file: ${filePath}`);

  const lastIndex = filePath.lastIndexOf('/');
  let folderName = '';
  let fileName = filePath;
  if (lastIndex > -1) {
    folderName = filePath.slice(0, lastIndex);
    fileName = filePath.slice(lastIndex + 1);
  }

  console.log(`[getTdoByFilePath] Parsed - Folder: "${folderName}", File: "${fileName}"`);

  const treeObjectId = await getFolderTreeObjectId(
    endpoint,
    token,
    veritoneAppId,
    folderName
  );

  console.log(`[getTdoByFilePath] Tree object ID for folder "${folderName}": ${treeObjectId}`);

  if (!treeObjectId) {
    console.log(`[getTdoByFilePath] No tree object ID found for folder: ${folderName}`);
    return null;
  }

  const tdos = await getTdosByFolderId(
    endpoint,
    token,
    veritoneAppId,
    treeObjectId
  );

  console.log(`[getTdoByFilePath] Found ${tdos?.length || 0} TDOs in folder "${folderName}"`);

  if (tdos && tdos.length > 0) {
    console.log(`[getTdoByFilePath] TDO filenames in folder:`,
      tdos.map(tdo => tdo?.context?.[0]?.['veritone-file']?.fileName).filter(Boolean)
    );
  }

  for (const tdo of tdos) {
    const tdoId = tdo?.recording?.recordingId;
    if (!tdoId) {
      continue;
    }
    const tdoFileName = tdo?.context?.[0]?.['veritone-file']?.fileName;
    console.log(`[getTdoByFilePath] Comparing "${tdoFileName}" with "${fileName}"`);

    if (tdoFileName === fileName) {
      console.log(`[getTdoByFilePath] Found matching TDO for file: ${fileName}`);
      return tdo;
    }
  }

  console.log(`[getTdoByFilePath] No matching TDO found for file: ${fileName}`);
  return null;
}

export async function getTdosByFolder(
  endpoint: string,
  token: string,
  veritoneAppId: string,
  folderName: string
) {
  const treeObjectId = await getFolderTreeObjectId(
    endpoint,
    token,
    veritoneAppId,
    folderName
  );
  const results = await getTdosByFolderId(
    endpoint,
    token,
    veritoneAppId,
    treeObjectId
  );
  return results;
}

export async function getTdosByFolderId(
  endpoint: string,
  token: string,
  veritoneAppId: string,
  treeObjectId: string | null
) {
  if (!treeObjectId) {
    return [];
  }

  const query = `
    query searchMedia($search: JSONData!){
      searchMedia(search: $search) {
        jsondata
      }
    }`;

  const variables = {
    search: {
      index: ['mine'],
      select: ['veritone-file'],
      limit: 1000,
      offset: 0,
      query: {
        operator: 'and',
        conditions: [
          {
            field: 'parentTreeObjectIds',
            operator: 'terms',
            values: [treeObjectId],
          },
        ],
      },
    },
  };

  const response = await fetchGraphQLApi<{
    searchMedia: {
      jsondata: {
        results: unknown & { recording: { recordingId: string } }[];
      };
    };
  }>({
    endpoint,
    token,
    veritoneAppId,
    query,
    variables,
  });
  const tdos = response?.data?.searchMedia?.jsondata?.results || [];
  const map = new Map();
  for (const tdo of tdos) {
    const tdoId = tdo?.recording?.recordingId;
    if (!tdoId) {
      continue;
    }
    map.set(tdoId, tdo);
  }
  return Array.from(map.values());
}

export async function deleteTDO(
  endpoint: string,
  token: string,
  veritoneAppId: string,
  tdoIds?: string[]
) {
  if (!tdoIds || !tdoIds.length) {
    return [];
  }
  const deleteTdos = tdoIds.map(
    (tdoId) => `tdo_${tdoId}: deleteTDO(id: "${tdoId}") { id }`
  );
  const query = `
    mutation deleteTDO{
      ${deleteTdos.join('\n')}
  }`;
  const response = await fetchGraphQLApi<{
    [key: string]: { id: string };
  }>({
    endpoint,
    token,
    veritoneAppId,
    query,
  });
  const deletedTdo = response?.data ? Object.values(response.data) : [];
  return deletedTdo.filter((tdo) => tdo?.id).map((tdo) => tdo.id);
}
