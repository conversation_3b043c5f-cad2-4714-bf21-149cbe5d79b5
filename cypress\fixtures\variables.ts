// there are 3 seeded files in folder e2e/mediaDetail folder
// the file tab name is 'FILES(3)'. If the number of files
// in e2e/mediaDetail folder changed, then fileTabName
// need to updated accordingly
export const fileTabName = 'FILES(3)';

// the destination folder name for testing moving folder
export const movedFolder = 'moveDest/e2e_test_folder_renamed';

// The transcript of e2e_video.mp4 has bookmark 'bookmarkSearch'.
export const bookmarkName = 'bookmarkSearch';

export const veritoneAppId = '8b3eac1c-5150-448e-8d99-fb7b860e7e41';

export const categoryIdTranscription = '67cd4dd0-2f75-445d-a6f0-2f297d6cd182';
export const engineIdTranscription = 'c0e55cde-340b-44d7-bb42-2e0d65e98255';
export const engineIdGoogleTranscription =
  'd12e8f6d-9285-4f7d-aa2e-9e6151206277';
export const categoryIdSpeakerDetection =
  'a856c447-1030-4fb0-917f-08179f949c4e';
export const engineIdSpeakerSeparation = '02f4d710-cbcd-4534-98d6-a31522f8f4a6';
export const engineIdSpeechmatics = '06c3f1d7-7424-407b-a3b5-6ef61154fc0b';
export const categoryIdTextExtraction = 'ba2a423e-99c9-4422-b3a5-0b188d8388ab';
export const engineIdTextExtraction = '62f2b2a7-bfd6-44c8-ab78-c02b47f95974';
export const categoryIdTranslate = '3b2b2ff8-44aa-4db4-9b71-ff96c3bf5923';
export const engineIdTranslateSpanishToEnglish =
  '95c910f6-4a26-4b66-96cb-488befa86466';
export const categoryIdFacialDetection = '6faad6b7-0837-45f9-b161-2f6bf31b7a07';
export const engineIdFaceboxSimilarity = 'ab682a42-ffdb-40cd-a8f7-432905f0a5a1';

export const categoryIdObjectDetection = '088a31be-9bd6-4628-a6f0-e4004e362ea0';
export const engineIdMachineboxTagbox = 'd66f553d-3cef-4c5a-9b66-3e551cc48b4b';

export const contentTemplateName = '8c61fc4c-953f-4be8-b091-88e4069a9106';
export const contentTemplateId = '8c61fc4c-953f-4be8-b091-88e4069a9106';

export const engineIdAmazonTranslate = '1fc4d3d4-54ab-42d1-882c-cfc9df42f386';

export const TestFile = {
  ImagePlantJpeg: {
    name: 'image-plant-jpeg.jpeg',
    path: '../setup/image-plant-jpeg.jpeg',
    type: 'image/jpeg',
  },
  E2eAudio: {
    name: 'e2e_audio.mp3',
    path: '../setup/e2e_audio.mp3',
    type: 'audio/mp3',
  },
  E2eVideoNoAudio: {
    name: 'e2e_video_no_audio.mp4',
    path: '../setup/e2e_video_no_audio.mp4',
    type: 'video/mp4',
  },
  ImagePng: {
    name: 'image-png.png',
    path: '../setup/image-png.png',
    type: 'image/png',
  },
  E2eVideo: {
    name: 'e2e_video.mp4',
    path: '../setup/e2e_video.mp4',
    type: 'video/mp4',
  },
  Bloomberg: {
    name: 'bloomberg.mp4',
    path: '../setup/bloomberg.mp4',
    type: 'video/mp4',
  },
  SpanishEmail: {
    name: 'spanish-email.eml',
    path: '../setup/spanish-email.eml',
    type: 'message/rfc822',
  },
  SpanishMp3: {
    name: 'spanish-mp3.mp3',
    path: '../setup/spanish-mp3.mp3',
    type: 'audio/mp3',
  },
  SpanishPdf: {
    name: 'spanish-pdf.pdf',
    path: '../setup/spanish-pdf.pdf',
    type: 'application/pdf',
  },
  SpanishTxt: {
    name: 'spanish-txt.txt',
    path: '../setup/spanish-txt.txt',
    type: 'text/plain',
  },
  SwahiliMp4: {
    name: 'swahili-mp4.mp4',
    path: '../setup/swahili-mp4.mp4',
    type: 'video/mp4',
  },
};
